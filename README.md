# تطبيق حساب الخلطة

تطبيق ويب لحساب نسبة الألياف والتكلفة الإجمالية للخلطات العلفية.

## المميزات

- ✅ إضافة خلطات جديدة مع حساب نسبة الألياف والتكلفة
- ✅ قائمة منسدلة بالمواد الشائعة مع إمكانية إضافة مواد جديدة
- ✅ التحقق من صحة البيانات المدخلة
- ✅ حفظ الخلطات في قاعدة البيانات
- ✅ عرض تفاصيل الخلطات المحفوظة
- ✅ حذف الخلطات غير المرغوب فيها
- ✅ واجهة مستخدم باللغة العربية مع دعم RTL
- ✅ رسائل تنبيه واضحة للأخطاء والنجاح

## المتطلبات

- Python 3.7+
- Flask
- Flask-SQLAlchemy
- Bootstrap-Flask

## التثبيت

1. استنسخ المشروع أو حمل الملفات
2. قم بتثبيت المتطلبات:
```bash
pip install -r requirements.txt
```

## تشغيل التطبيق

```bash
python app.py
```

سيعمل التطبيق على `http://localhost:5000`

## الاستخدام

### إضافة خلطة جديدة

1. أدخل اسم الخلطة
2. لكل مادة:
   - اختر المادة من القائمة المنسدلة أو اختر "أخرى" لإدخال مادة جديدة
   - أدخل الكمية بالكيلوغرام
   - أدخل نسبة الألياف (%)
   - أدخل السعر لكل كيلوغرام
3. اضغط "حساب وحفظ الخلطة"

### عرض الخلطات المحفوظة

- ستظهر جميع الخلطات في قائمة أسفل النموذج
- اضغط "عرض التفاصيل" لرؤية تفاصيل كل مادة في الخلطة
- اضغط "حذف" لحذف خلطة (مع تأكيد)

## المواد المتاحة مسبقاً

- برسيم حجازي
- شعير
- ذرة صفراء
- نخالة قمح
- كسبة فول الصويا
- كسبة بذرة القطن
- دريس رودس
- تبن قمح
- علف مركز
- ملح طعام
- حجر جيري

## التحقق من صحة البيانات

التطبيق يتحقق من:
- وجود اسم للخلطة
- وجود مادة واحدة على الأقل
- صحة الأرقام المدخلة
- أن تكون جميع القيم موجبة
- أن تكون نسبة الألياف بين 0-100%

## تشغيل الاختبارات

```bash
python test_app.py
```

## هيكل المشروع

```
├── app.py              # التطبيق الرئيسي
├── requirements.txt    # المتطلبات
├── test_app.py        # ملف الاختبارات
├── README.md          # هذا الملف
├── templates/         # قوالب HTML
│   ├── index.html     # الصفحة الرئيسية
│   └── view_mix.html  # صفحة عرض تفاصيل الخلطة
└── instance/          # قاعدة البيانات (تُنشأ تلقائياً)
    └── database.db
```

## التطوير المستقبلي

- إضافة إمكانية تعديل الخلطات
- إضافة إمكانية تصدير البيانات
- إضافة إحصائيات ومخططات
- إضافة نظام مستخدمين
- إضافة إمكانية البحث والفلترة
