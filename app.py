from flask import Flask, render_template, request, redirect, url_for, flash
from flask_sqlalchemy import SQLAlchemy
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your_secret_key'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///database.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)

class Mix(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    total_fiber = db.Column(db.Float, nullable=False)
    total_cost = db.Column(db.Float, nullable=False)
    items = db.Column(db.Text, nullable=False)  # JSON string of items


@app.route('/', methods=['GET', 'POST'])
def index():
    if request.method == 'POST':
        mix_name = request.form.get('mix_name')
        items = []
        total_fiber = 0
        total_cost = 0
        for i in range(1, 11):  # Up to 10 items
            name = request.form.get(f'name_{i}')
            qty = request.form.get(f'qty_{i}')
            fiber = request.form.get(f'fiber_{i}')
            price = request.form.get(f'price_{i}')
            if name and qty and fiber and price:
                qty = float(qty)
                fiber = float(fiber)
                price = float(price)
                items.append({'name': name, 'qty': qty, 'fiber': fiber, 'price': price})
                total_fiber += (fiber * qty)
                total_cost += (price * qty)
        total_qty = sum([item['qty'] for item in items])
        fiber_percent = (total_fiber / total_qty) if total_qty else 0
        # Save to DB
        import json
        mix = Mix(name=mix_name, total_fiber=fiber_percent, total_cost=total_cost, items=json.dumps(items, ensure_ascii=False))
        db.session.add(mix)
        db.session.commit()
        flash('تم حفظ الخلطة بنجاح!', 'success')
        return redirect(url_for('index'))
    mixes = Mix.query.order_by(Mix.id.desc()).all()
    return render_template('index.html', mixes=mixes)

@app.route('/mix/<int:mix_id>')
def view_mix(mix_id):
    import json
    mix = Mix.query.get_or_404(mix_id)
    items = json.loads(mix.items)
    return render_template('view_mix.html', mix=mix, items=items)

if __name__ == '__main__':
    db.create_all()
    app.run(debug=True)
