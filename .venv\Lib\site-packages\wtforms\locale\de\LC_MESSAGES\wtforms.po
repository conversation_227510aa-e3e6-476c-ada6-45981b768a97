# German translations for WTForms.
# Copyright (C) 2020 WTForms Team
# This file is distributed under the same license as the WTForms project.
# <AUTHOR> <EMAIL>, 2020.
#
msgid ""
msgstr ""
"Project-Id-Version: WTForms 1.0.4\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2024-01-11 08:20+0100\n"
"PO-Revision-Date: 2013-05-13 19:27+0100\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: de <<EMAIL>>\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1)\n"
"Generated-By: Babel 2.8.0\n"

#: src/wtforms/validators.py:86
#, python-format
msgid "Invalid field name '%s'."
msgstr "Ungültiger Feldname '%s'."

#: src/wtforms/validators.py:99
#, python-format
msgid "Field must be equal to %(other_name)s."
msgstr "Feld muss gleich wie %(other_name)s sein."

#: src/wtforms/validators.py:145
#, python-format
msgid "Field must be at least %(min)d character long."
msgid_plural "Field must be at least %(min)d characters long."
msgstr[0] "Feld muss mindestens %(min)d Zeichen beinhalten."
msgstr[1] "Feld muss mindestens %(min)d Zeichen beinhalten."

#: src/wtforms/validators.py:151
#, python-format
msgid "Field cannot be longer than %(max)d character."
msgid_plural "Field cannot be longer than %(max)d characters."
msgstr[0] "Feld kann nicht länger als %(max)d Zeichen sein."
msgstr[1] "Feld kann nicht länger als %(max)d Zeichen sein."

#: src/wtforms/validators.py:157
#, python-format
msgid "Field must be exactly %(max)d character long."
msgid_plural "Field must be exactly %(max)d characters long."
msgstr[0] ""
msgstr[1] ""

#: src/wtforms/validators.py:163
#, python-format
msgid "Field must be between %(min)d and %(max)d characters long."
msgstr "Feld muss zwischen %(min)d und %(max)d Zeichen beinhalten."

#: src/wtforms/validators.py:216
#, python-format
msgid "Number must be at least %(min)s."
msgstr "Zahl muss mindestens %(min)s sein."

#: src/wtforms/validators.py:219
#, python-format
msgid "Number must be at most %(max)s."
msgstr "Zahl kann höchstens %(max)s sein."

#: src/wtforms/validators.py:222
#, python-format
msgid "Number must be between %(min)s and %(max)s."
msgstr "Zahl muss zwischen %(min)s und %(max)s liegen."

#: src/wtforms/validators.py:293 src/wtforms/validators.py:323
msgid "This field is required."
msgstr "Dieses Feld wird benötigt."

#: src/wtforms/validators.py:358
msgid "Invalid input."
msgstr "Ungültige Eingabe."

#: src/wtforms/validators.py:422
msgid "Invalid email address."
msgstr "Ungültige E-Mail-Adresse."

#: src/wtforms/validators.py:460
msgid "Invalid IP address."
msgstr "Ungültige IP-Adresse."

#: src/wtforms/validators.py:503
msgid "Invalid Mac address."
msgstr "Ungültige Mac-Adresse."

#: src/wtforms/validators.py:540
msgid "Invalid URL."
msgstr "Ungültige URL."

#: src/wtforms/validators.py:561
msgid "Invalid UUID."
msgstr "Ungültige UUID."

#: src/wtforms/validators.py:594
#, python-format
msgid "Invalid value, must be one of: %(values)s."
msgstr "Ungültiger Wert. Mögliche Werte: %(values)s."

#: src/wtforms/validators.py:629
#, python-format
msgid "Invalid value, can't be any of: %(values)s."
msgstr "Ungültiger Wert. Wert kann keiner von folgenden sein: %(values)s."

#: src/wtforms/validators.py:698
#, fuzzy
#| msgid "This field is required."
msgid "This field cannot be edited."
msgstr "Dieses Feld wird benötigt."

#: src/wtforms/validators.py:714
msgid "This field is disabled and cannot have a value."
msgstr ""

#: src/wtforms/csrf/core.py:96
msgid "Invalid CSRF Token."
msgstr "Ungültiger CSRF-Code."

#: src/wtforms/csrf/session.py:63
msgid "CSRF token missing."
msgstr "CSRF-Code nicht vorhanden."

#: src/wtforms/csrf/session.py:71
msgid "CSRF failed."
msgstr "CSRF fehlgeschlagen."

#: src/wtforms/csrf/session.py:76
msgid "CSRF token expired."
msgstr "CSRF-Code verfallen."

#: src/wtforms/fields/choices.py:142
msgid "Invalid Choice: could not coerce."
msgstr "Ungültige Auswahl: Konnte nicht umwandeln."

#: src/wtforms/fields/choices.py:149 src/wtforms/fields/choices.py:203
msgid "Choices cannot be None."
msgstr ""

#: src/wtforms/fields/choices.py:155
msgid "Not a valid choice."
msgstr "Keine gültige Auswahl."

#: src/wtforms/fields/choices.py:193
msgid "Invalid choice(s): one or more data inputs could not be coerced."
msgstr ""
"Ungültige Auswahl: Einer oder mehrere Eingaben konnten nicht umgewandelt "
"werden."

#: src/wtforms/fields/choices.py:214
#, fuzzy, python-format
#| msgid "'%(value)s' is not a valid choice for this field."
msgid "'%(value)s' is not a valid choice for this field."
msgid_plural "'%(value)s' are not valid choices for this field."
msgstr[0] "'%(value)s' ist kein gültige Auswahl für dieses Feld."
msgstr[1] "'%(value)s' ist kein gültige Auswahl für dieses Feld."

#: src/wtforms/fields/datetime.py:51
msgid "Not a valid datetime value."
msgstr "Kein gültiges Datum mit Zeit."

#: src/wtforms/fields/datetime.py:77
msgid "Not a valid date value."
msgstr "Kein gültiges Datum."

#: src/wtforms/fields/datetime.py:103
msgid "Not a valid time value."
msgstr ""

#: src/wtforms/fields/datetime.py:148
#, fuzzy
#| msgid "Not a valid date value."
msgid "Not a valid week value."
msgstr "Kein gültiges Datum."

#: src/wtforms/fields/numeric.py:82 src/wtforms/fields/numeric.py:92
msgid "Not a valid integer value."
msgstr "Keine gültige, ganze Zahl."

#: src/wtforms/fields/numeric.py:168
msgid "Not a valid decimal value."
msgstr "Keine gültige Dezimalzahl."

#: src/wtforms/fields/numeric.py:197
msgid "Not a valid float value."
msgstr "Keine gültige Gleitkommazahl."
