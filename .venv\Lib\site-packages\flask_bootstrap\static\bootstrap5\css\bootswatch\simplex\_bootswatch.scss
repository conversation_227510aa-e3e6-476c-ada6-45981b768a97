// Simplex 5.3.5
// Bootswatch


// Variables

$web-font-path: "https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;700&display=swap" !default;
@if $web-font-path {
  @import url("#{$web-font-path}");
}

// Mixins

@mixin btn-shadow($color){
  @include gradient-y-three-colors(tint-color($color, 6%), $color, 6%, shade-color($color, 6%));
  filter: none;
  border: 1px solid shade-color($color, 13%);
}

// Navbar

.navbar {
  border-style: solid;
  border-width: 1px;

  &-fixed-top {
    border-width: 0 0 1px;
  }

  &-fixed-bottom {
    border-style: solid;
    border-top-width: 1px 0 0 0;
  }

  &.bg-primary {
    border-color: shade-color($primary, 13%) !important;
  }

  &.bg-dark {
    border-color: shade-color($dark, 13%) !important;
  }

  &.bg-light {
    border-color: shade-color($white, 13%);
  }
}

// Buttons

.btn-primary,
.btn-primary:hover {
  @include btn-shadow($primary);
}

.btn-secondary,
.btn-secondary:hover {
  @include btn-shadow($secondary);
}

.btn-secondary:focus,
.btn-secondary:not([disabled]):not(.disabled):active,
.btn-secondary:not([disabled]):not(.disabled).active {
  box-shadow: 0 0 0 .2rem rgba($gray-200, .5);
}

.btn-success,
.btn-success:hover {
  @include btn-shadow($success);
}

.btn-info,
.btn-info:hover {
  @include btn-shadow($info);
}

.btn-warning,
.btn-warning:hover {
  @include btn-shadow($warning);
}

.btn-danger,
.btn-danger:hover {
  @include btn-shadow($danger);
}

.btn-dark,
.btn-dark:hover {
  @include btn-shadow($dark);
}

.btn-light,
.btn-light:hover {
  @include btn-shadow($light);
}

.btn-outline-secondary {
  color: $gray-400;
  border-color: $gray-400;

  &:hover {
    color: $white;
    background-color: $gray-400;
  }
}

// Typography

.text-secondary {
  color: $gray-600 !important;
}

// Tables

.table-primary,
.table-secondary,
.table-success,
.table-warning,
.table-danger,
.table-info,
.table-light {
  --#{$prefix}table-color: #{$body-color};
}

// Forms

legend,
label {
  color: $headings-color;
}

// Navs

.breadcrumb {
  border: 1px solid shade-color($white, 13%);
}

.pagination {
  .page-link:hover {
    text-decoration: none;
  }
}

// Indicators

.badge {
  &.bg-secondary,
  &.bg-light {
    color: $dark;
  }
}
