// Materia 4.6.1
// Bootswatch

//
// Color system
//

$white:    #fff !default;
$gray-100: #f8f9fa !default;
$gray-200: #eee !default;
$gray-300: #dee2e6 !default;
$gray-400: #ced4da !default;
$gray-500: #bbb !default;
$gray-600: #666 !default;
$gray-700: #444 !default;
$gray-800: #222 !default;
$gray-900: #212121 !default;
$black:    #000 !default;

$blue:    #2196f3 !default;
$indigo:  #6610f2 !default;
$purple:  #6f42c1 !default;
$pink:    #e83e8c !default;
$red:     #e51c23 !default;
$orange:  #fd7e14 !default;
$yellow:  #ff9800 !default;
$green:   #4caf50 !default;
$teal:    #20c997 !default;
$cyan:    #9c27b0 !default;

$primary:       $blue !default;
$secondary:     $white !default;
$success:       $green !default;
$info:          $cyan !default;
$warning:       $yellow !default;
$danger:        $red !default;
$light:         $white !default;
$dark:          $gray-800 !default;

$yiq-contrasted-threshold:  170 !default;

$enable-gradients: true !default;

// Body

$body-color:    $gray-700 !default;

// Fonts

// stylelint-disable-next-line value-keyword-case
$font-family-sans-serif:    Roboto, -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", Arial, sans-serif !default;
$font-size-base:            .8125rem !default;
$font-weight-base:          400 !default;

// Buttons

$input-btn-padding-y:       .8rem !default;
$input-btn-padding-x:       1rem !default;

// Forms

$input-padding-y:                1rem !default;
$input-padding-x:                0 !default;
$input-padding-y-sm:             0 !default;
$input-padding-x-sm:             0 !default;
$input-padding-y-lg:             ($font-size-base * 1.25) !default;
$input-padding-x-lg:             0 !default;
$input-bg:                       transparent !default;
$input-disabled-bg:              transparent !default;
$input-color:                    $gray-600 !default;
$input-border-color:             transparent !default;
$input-border-width:             0 !default;
$input-border-radius:            0 !default;
$input-border-radius-lg:         0 !default;
$input-border-radius-sm:         0 !default;
$input-placeholder-color:        rgba(0, 0, 0, .4) !default;
$input-group-addon-bg:           transparent !default;
$custom-control-gutter:          1.75rem !default;
$custom-control-indicator-focus-box-shadow: 0 !default;
$custom-control-indicator-size:  1.25rem !default;
$custom-control-indicator-bg:    $white !default;
$custom-checkbox-indicator-border-radius: 2px !default;
$custom-control-indicator-disabled-bg: $gray-100 !default;
$custom-radio-indicator-icon-checked: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='4' fill='#{$blue}'/></svg>") !default;
$custom-select-border-radius:       0 !default;
$custom-select-box-shadow:          none !default;

// Navs

$nav-link-disabled-color:       $gray-500 !default;
$nav-tabs-border-color:         transparent !default;

// Navbar

$navbar-padding-y:                  1rem !default;
$navbar-dark-color:                 rgba($white, .75) !default;
$navbar-dark-hover-color:           $white !default;

// Cards

$card-border-width:                 0 !default;
$card-border-color:                 transparent !default;

// Tooltips

$tooltip-bg:                        $gray-700 !default;

// Modals

$modal-content-border-color:        transparent !default;

// Progress bars

$progress-height:               .375rem !default;
$progress-border-radius:        0 !default;

// Close

$close-font-size:             2.125rem !default;
$close-font-weight:           300 !default;

// Box shadows

$enable-shadows: true !default;
$btn-box-shadow:         0 1px 4px rgba($black, .4) !default;
$btn-active-box-shadow:  2px 2px 4px rgba($black, .4) !default;
$btn-focus-box-shadow:   0 0 0 2px mix(rgba($white, .5), rgba($black, .5), 80%) !default;
$input-box-shadow:       inset 0 -1px 0 mix($white, $black, 86.7%) !default;
$input-focus-box-shadow: inset 0 -2px 0 $primary !default;
