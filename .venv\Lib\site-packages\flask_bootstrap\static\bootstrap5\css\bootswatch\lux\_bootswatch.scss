// Lux 5.3.5
// Bootswatch


// Variables

$web-font-path: "https://fonts.googleapis.com/css2?family=Nunito+Sans:wght@200;300;400;500;600;700&display=swap" !default;
@if $web-font-path {
  @import url("#{$web-font-path}");
}

:root {
  --bs-border-width: 0;
}

// Navbar

.navbar {
  font-size: $font-size-sm;
  font-weight: 600;
  text-transform: uppercase;

  &-nav {
    .nav-link {
      padding-top: .715rem;
      padding-bottom: .715rem;
    }
  }

  &-brand {
    margin-right: 2rem;
  }
}

.bg-light {
  border: 1px solid rgba(0, 0, 0, .1);

  &.navbar-fixed-top {
    border-width: 0 0 1px;
  }

  &.navbar-bottom-top {
    border-width: 1px 0 0;
  }
}

.nav-item {
  margin-right: 2rem;
}

// Buttons

.btn {
  font-size: $font-size-sm;
  text-transform: uppercase;

  &-sm {
    font-size: 10px;
  }

  &-warning {
    &,
    &:hover,
    &:not([disabled]):not(.disabled):active,
    &:focus {
      color: $white;
    }
  }
}

.btn-outline-secondary {
  color: $gray-600;
  border-color: $gray-600;

  &.disabled,
  &:disabled,
  fieldset:disabled & {
    color: $gray-600;
    border-color: $gray-600;
  }

  &:not([disabled]):not(.disabled):hover,
  &:not([disabled]):not(.disabled):focus,
  &:not([disabled]):not(.disabled):active {
    color: $white;
    background-color: $gray-400;
    border-color: $gray-400;
  }

  &:not([disabled]):not(.disabled):focus {
    box-shadow: 0 0 0 .2rem rgba($gray-400, .5);
  }
}

[class*="btn-outline-"] {
  border-width: 2px;
}

.border-secondary {
  border: 1px solid $gray-400 !important;
}

@include color-mode(dark) {
  .btn-primary {
    background-color: #080808;
  }
}

// Typography

body {
  letter-spacing: 1px;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  text-transform: uppercase;
  letter-spacing: 3px;
}

.text-secondary {
  color: $body-color !important;
}

// Tables

th {
  font-size: $font-size-sm;
  text-transform: uppercase;
}

.table {
  th,
  td {
    padding: 1.5rem;
  }

  &-sm {
    th,
    td {
      padding: .75rem;
    }
  }
}

.table-primary,
.table-secondary,
.table-success,
.table-warning,
.table-danger,
.table-info,
.table-light {
  --#{$prefix}table-color: #{$body-color};
}

// Forms

@include color-mode(dark) {
  .form-control {
    color: $body-color;

    &::placeholder {
      color: $body-secondary-color;
    }
  }

  .form-floating {
    > label,
    > .form-control:focus ~ label,
    > .form-control:not(:placeholder-shown) ~ label,
    > .form-control-plaintext ~ label,
    > .form-select ~ label {
      color: $body-secondary-color;
    }

    > .form-control::placeholder,
    > .form-control-plaintext::placeholder {
      color: transparent;
    }
  }

  .form-check-input:checked {
    background-color: $body-secondary-color;
  }

  .form-switch .form-check-input {
    background-image: $form-switch-bg-image;
  }

  .form-range::-webkit-slider-thumb {
    background-color: $gray-600;
  }

  .form-range:disabled::-webkit-slider-thumb {
    background-color: $gray-800;
  }

  .input-group-text {
    color: $body-secondary-color;
  }
}

// Navs

.dropdown-menu {
  font-size: $font-size-sm;
  text-transform: none;
}

// Indicators

.badge {
  padding-top: .28rem;

  &-pill {
    border-radius: 10rem;
  }

  &.bg-secondary,
  &.bg-light {
    color: $dark;
  }
}

// Containers

.list-group-item,
.card {
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  .h1,
  .h2,
  .h3,
  .h4,
  .h5,
  .h6 {
    color: inherit;
  }
}
