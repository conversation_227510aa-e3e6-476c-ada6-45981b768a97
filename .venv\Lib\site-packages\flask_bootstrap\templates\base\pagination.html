{#  This file was part of Flask-Bootstrap and was modified under the terms of
 its BSD License. Copyright (c) 2013, <PERSON>. All rights reserved. #}
{% from 'base/utils.html' import arg_url_for %}

{% macro render_pager(pagination,
                      fragment='',
                      prev=('<span aria-hidden="true">&larr;</span> Previous')|safe,
                      next=('Next <span aria-hidden="true">&rarr;</span>')|safe,
                      align='') -%}
    <nav aria-label="Page navigation">
        <ul class="pagination{% if align == 'center' %} justify-content-center{% elif align == 'right' %} justify-content-end{% endif %}">
            <li class="page-item{% if not pagination.has_prev %} disabled{% endif %}">
                <a class="page-link"
                   href="{{ url_for(request.endpoint, page=pagination.prev_num, **kwargs) + fragment if pagination.has_prev else '#' }}">
                    {{ prev }}
                </a>
            </li>
            <li class="page-item{% if not pagination.has_next %} disabled{% endif %}">
                <a class="page-link"
                   href="{{ url_for(request.endpoint, page=pagination.next_num, **kwargs) + fragment if pagination.has_next else '#' }}">
                    {{ next }}
                </a>
            </li>
        </ul>
    </nav>
{%- endmacro %}

{% macro render_pagination(pagination,
                           endpoint=None,
                           prev=('&laquo;')|safe,
                           next=('&raquo;')|safe,
                           size=None,
                           ellipses='…',
                           args={},
                           fragment='',
                           align=''
                           )-%}
    {% if fragment != '' and not fragment.startswith('#') %}{% set fragment = '#' + fragment %}{% endif %}
    {% with url_args = {} %}
        {%- do url_args.update(request.view_args if not endpoint else {}),
       url_args.update(request.args if not endpoint else {}),
       url_args.update(args) -%}
        {% with endpoint = endpoint or request.endpoint %}
            <nav aria-label="Page navigation">
                <ul class="pagination{% if size %} pagination-{{ size }}{% endif %}{% if align == 'center' %} justify-content-center{% elif align == 'right' %} justify-content-end{% endif %}"{{ kwargs|xmlattr }}>
                    {# prev and next are only show if a symbol has been passed. #}
                    {% if prev != None -%}
                        <li class="page-item{% if not pagination.has_prev %} disabled{% endif %}">
                            <a class="page-link" href="{{ arg_url_for(endpoint, url_args, page=pagination.prev_num) if pagination.has_prev else '#' }}{{ fragment }}">{{ prev }}</a>
                        </li>
                    {%- endif -%}

                    {%- for page in pagination.iter_pages() %}
                        {% if page %}
                            {% if page != pagination.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ arg_url_for(endpoint, url_args, page=page) }}{{ fragment }}">{{ page }}</a>
                                </li>
                            {% else %}
                                {{ get_current_page(page) }}
                            {% endif %}
                        {% elif ellipses != None %}
                            <li class="page-item disabled"><a class="page-link" href="#">{{ ellipses }}</a></li>
                        {% endif %}
                    {%- endfor %}

                    {% if next != None -%}
                        <li class="page-item{% if not pagination.has_next %} disabled{% endif %}">
                            <a class="page-link" href="{{ arg_url_for(endpoint, url_args, page=pagination.next_num) if pagination.has_next else '#' }}{{ fragment }}">{{ next }}</a>
                        </li>
                    {%- endif -%}
                </ul>
            </nav>
        {% endwith %}
    {% endwith %}
{% endmacro %}
