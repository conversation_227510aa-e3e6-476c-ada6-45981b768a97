{% extends 'base/nav.html' %}

{% macro render_nav_item(endpoint, text, _badge='', _use_li=False, _badge_classes='badge text-bg-light') %}
    {% set active = True if request.endpoint and request.endpoint == endpoint else False %}
    {% if _use_li %}<li class="nav-item">{% endif %}
    <a class="{% if not _use_li %}nav-item {% endif %}nav-link{% if active %} active" aria-current="page{% endif %}"
       href="{{ url_for(endpoint, **kwargs) }}">
        {{ text }} {% if _badge %}<span class="{{ _badge_classes }}">{{ _badge }}</span>{% endif %}
    </a>
    {% if _use_li %}</li>{% endif %}
{% endmacro %}
