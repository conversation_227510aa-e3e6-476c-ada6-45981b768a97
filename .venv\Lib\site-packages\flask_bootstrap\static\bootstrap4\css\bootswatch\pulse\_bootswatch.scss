// Pulse 4.6.1
// Bootswatch

// Buttons =====================================================================

.btn {
  &:focus,
  &:active,
  &:active:focus,
  &.active:focus {
    outline: none;
  }

  &-secondary {
    background-color: $white;
    border-color: #ccc;
    color: $gray-900;

    &:hover {
      background-color: $gray-300;
      border-color: $gray-500;
      color: $gray-900;
    }

    &.disabled {
      background-color: $white;
      border-color: lighten(#ccc, 5%);
      color: lighten($gray-900, 5%);
    }
  }

  &-warning {
    color: $white;
  }

  &-primary:focus {
    box-shadow: 0 0 5px lighten($primary, 10%);
  }

  &-secondary:focus {
    box-shadow: 0 0 5px $gray-400;
  }

  &-success:focus {
    box-shadow: 0 0 5px lighten($success, 10%);
  }

  &-info:focus {
    box-shadow: 0 0 5px lighten($info, 10%);
  }

  &-warning:focus {
    box-shadow: 0 0 5px lighten($warning, 10%);
  }

  &-danger:focus {
    box-shadow: 0 0 5px lighten($danger, 10%);
  }

  &.disabled:focus {
    box-shadow: none;
  }
}

// Tables ======================================================================

.table .thead-dark th {
  background-color: $secondary;
  border-color: $table-border-color;
}

// Forms =======================================================================

.form-control:focus {
  box-shadow: 0 0 5px rgba(100, 65, 164, .4);
}

// Navs ========================================================================

.nav-tabs {
  .nav-link,
  .nav-link.active, {
    border-width: 0 0 1px;
  }

  .nav-link:hover,
  .nav-link.active,
  .nav-link.active:hover,
  .nav-link.active:focus {
    border-bottom: 1px solid $primary;
  }

  .nav-item + .nav-item {
    margin-left: 0;
  }
}

.breadcrumb {
  &-item.active {
    color: $gray-700;
  }
}

// Indicators ==================================================================

.badge {
  padding-bottom: .4em;

  &-secondary,
  &-warning {
    color: $white;
  }
}

// Progress bars ===============================================================

.progress {
  height: 8px;
}

// Containers ==================================================================

.list-group {
  &-item {
    color: rgba(255, 255, 255, .8);

    &.active,
    &:hover,
    &:focus {
      color: $white;
    }

    &.active {
      font-weight: 700;

      &:hover {
        background-color: $list-group-hover-bg;
      }
    }

    &.disabled:hover {
      color: $list-group-disabled-color;
    }
  }
}
