{% macro render_static(type, filename_or_url, local=True) %}
    {% if local -%}{% set filename_or_url = url_for('static', filename=filename_or_url) %}{%- endif %}
    {% if type == 'css' -%}
        <link rel="stylesheet" href="{{ filename_or_url }}" type="text/css">
    {%- elif type == 'js' -%}
        <script type="text/javascript" src="{{ filename_or_url }}"></script>
    {%- elif type == 'icon' -%}
        <link rel="icon" href="{{ filename_or_url }}">
    {%- endif %}
{% endmacro %}


{% macro render_icon(name, size=config.BOOTSTRAP_ICON_SIZE, color=config.BOOTSTRAP_ICON_COLOR, title=None, desc=None, classes=None, font=config.BOOTSTRAP_ICON_USE_FONT) %}
{% set bootstrap_colors = ['primary', 'secondary', 'success', 'danger', 'warning', 'info', 'light', 'dark', 'muted'] %}
{%- if font == true -%}
<i class="bi-{{ name }}{% if color in bootstrap_colors %} text-{{ color }}{% endif %}" style="{% if color and color not in bootstrap_colors %}color: {{ color }}; {% endif %}font-size: {{ size }};"></i>
{%- else -%}
<svg class="bi{% if not color %}{% if classes %} {{ classes }}{% endif %}"
{%- elif color in bootstrap_colors %} text-{{ color }}"{% else %}" style="color: {{ color }}"{% endif -%}
{%- if size %} width="{{ size }}"{% endif %}{% if size %} height="{{ size }}"{% endif %} fill="currentColor">
    {% if title is not none %}<title>{{ title }}</title>{% endif %}
    {% if desc is not none %}<desc>{{ desc }}</desc>{% endif %}
    <use xlink:href="{{ url_for('bootstrap.static', filename='icons/bootstrap-icons.svg') }}#{{ name }}"/>
</svg>
{%- endif -%}
{% endmacro %}


{% macro arg_url_for(endpoint, base) %}
    {# calls url_for() with a given endpoint and **base as the parameters,
   additionally passing on all keyword_arguments (may overwrite existing ones)
 #}
    {%- with kargs = base.copy() -%}
        {%- do kargs.update(kwargs) -%}
        {{ url_for(endpoint, **kargs) }}
    {%- endwith %}
{%- endmacro %}
