<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>حساب الخلطة</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
</head>
<body class="bg-light">
<div class="container py-4">
    <h2 class="mb-4">حساب نسبة الألياف والتكلفة الإجمالية</h2>
    {% with messages = get_flashed_messages(with_categories=true) %}
      {% if messages %}
        {% for category, message in messages %}
          <div class="alert alert-{{ 'danger' if category == 'error' else 'success' if category == 'success' else category }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>
        {% endfor %}
      {% endif %}
    {% endwith %}
    <form method="post" class="card p-3 mb-4">
        <div class="mb-3">
            <label class="form-label">اسم الخلطة</label>
            <input type="text" name="mix_name" class="form-control" required>
        </div>
        <div class="row fw-bold text-center">
            <div class="col">المكون</div>
            <div class="col">الكمية (كغ)</div>
            <div class="col">نسبة الألياف (%)</div>
            <div class="col">السعر/كغ (دينار)</div>
        </div>
        {% for i in range(1, 11) %}
        <div class="row mb-2">
            <div class="col">
                <select name="name_select_{{i}}" class="form-select" onchange="toggleOtherInput(this, '{{i}}')">
                    <option value="">اختر المكون</option>
                    {% for material in common_materials %}
                    <option value="{{ material }}">{{ material }}</option>
                    {% endfor %}
                </select>
                <input type="text" name="name_{{i}}" class="form-control mt-1 d-none" placeholder="اكتب اسم المكون" />
            </div>
            <div class="col"><input type="number" step="0.01" min="0" name="qty_{{i}}" class="form-control" placeholder="0.00"></div>
            <div class="col"><input type="number" step="0.01" min="0" max="100" name="fiber_{{i}}" class="form-control" placeholder="0.00"></div>
            <div class="col"><input type="number" step="0.01" min="0" name="price_{{i}}" class="form-control" placeholder="0.00"></div>
        </div>
        {% endfor %}
        <button type="submit" class="btn btn-primary mt-3">حساب وحفظ الخلطة</button>
    </form>
    <h4>الخلطات المحفوظة</h4>
    <ul class="list-group">
        {% for mix in mixes %}
        <li class="list-group-item d-flex justify-content-between align-items-center">
            <span>{{ mix.name }}</span>
            <span>
                نسبة الألياف: {{ '%.2f'|format(mix.total_fiber) }}% |
                التكلفة الإجمالية: {{ '%.2f'|format(mix.total_cost) }} د.أ
                <a href="{{ url_for('view_mix', mix_id=mix.id) }}" class="btn btn-sm btn-outline-info ms-2">عرض التفاصيل</a>
                <form method="post" action="{{ url_for('delete_mix', mix_id=mix.id) }}" class="d-inline ms-1" onsubmit="return confirm('هل أنت متأكد من حذف هذه الخلطة؟')">
                    <button type="submit" class="btn btn-sm btn-outline-danger">حذف</button>
                </form>
            </span>
        </li>
        {% else %}
        <li class="list-group-item">لا توجد خلطات محفوظة بعد.</li>
        {% endfor %}
    </ul>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
function toggleOtherInput(select, idx) {
    var input = select.parentElement.querySelector('input[name="name_' + idx + '"]');
    if (select.value === 'أخرى') {
        input.classList.remove('d-none');
        input.required = true;
    } else {
        input.classList.add('d-none');
        input.required = false;
        input.value = '';
    }
}
</script>
</body>
</html>
