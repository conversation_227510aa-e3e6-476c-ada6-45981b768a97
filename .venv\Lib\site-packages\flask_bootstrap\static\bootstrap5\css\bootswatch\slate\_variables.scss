// Slate 5.3.5
// Bootswatch

$theme: "slate" !default;

//
// Color system
//

$white:    #fff !default;
$gray-100: #f8f9fa !default;
$gray-200: #e9ecef !default;
$gray-300: #dee2e6 !default;
$gray-400: #ced4da !default;
$gray-500: #999 !default;
$gray-600: #7a8288 !default;
$gray-700: #52575c !default;
$gray-800: #3a3f44 !default;
$gray-900: #272b30 !default;
$black:    #000 !default;

$blue:    #007bff !default;
$indigo:  #6610f2 !default;
$purple:  #6f42c1 !default;
$pink:    #e83e8c !default;
$red:     #ee5f5b !default;
$orange:  #fd7e14 !default;
$yellow:  #f89406 !default;
$green:   #62c462 !default;
$teal:    #20c997 !default;
$cyan:    #5bc0de !default;

$primary:       $gray-800 !default;
$secondary:     $gray-600 !default;
$success:       $green !default;
$info:          $cyan !default;
$warning:       $yellow !default;
$danger:        $red !default;
$light:         $gray-200 !default;
$dark:          $gray-900 !default;

$primary-bg-subtle:       lighten($gray-800, 10%) !default;

$min-contrast-ratio:   1.95 !default;

// Body

$body-bg:                   $gray-900 !default;
$body-color:                #aaa !default;

// Links

$link-color:                $white !default;

// Fonts

// Tables

$table-color:                 initial !default;
$table-accent-bg:             rgba($white, .05) !default;
$table-hover-bg:              rgba($white, .075) !default;
$table-border-color:          rgba($black, .6) !default;
$table-dark-border-color:     $table-border-color !default;
$table-dark-color:            $white !default;

$table-bg-scale:              0% !default;

// Buttons

$input-btn-padding-y:         .75rem !default;
$input-btn-padding-x:         1rem !default;

// Forms

$input-bg:                          $white !default;
$input-color:                       $gray-900 !default;
$input-border-color:                rgba($black, .6) !default;
$input-group-addon-color:           $gray-500 !default;
$input-group-addon-bg:              $gray-700 !default;
$input-disabled-color:              $gray-600 !default;
$input-disabled-bg:                 #ccc !default;

$form-check-input-bg:                     $white !default;

$form-select-disabled-bg:           $input-disabled-bg !default;
$form-select-disabled-color:        $input-disabled-color !default;

$form-file-button-color:          $input-group-addon-color !default;
$form-file-button-bg:             $input-group-addon-bg !default;
$form-file-button-hover-bg:       darken($form-file-button-bg, 5%) !default;

// Dropdowns

$dropdown-bg:                       $gray-800 !default;
$dropdown-border-color:             rgba($black, .6) !default;
$dropdown-divider-bg:               rgba($black, .15) !default;
$dropdown-link-color:               $body-color !default;
$dropdown-link-hover-color:         $white !default;
$dropdown-link-hover-bg:            $body-bg !default;
$dropdown-link-active-color:        $dropdown-link-hover-color !default;
$dropdown-link-active-bg:           $dropdown-link-hover-bg !default;

// Navs

$nav-tabs-border-color:             rgba($black, .6) !default;
$nav-tabs-link-hover-border-color:  $nav-tabs-border-color !default;
$nav-tabs-link-active-color:        $white !default;
$nav-tabs-link-active-border-color: $nav-tabs-border-color !default;

$nav-underline-link-active-color:   $white !default;

// Navbar

$navbar-padding-y:                  0 !default;

// Pagination
$pagination-padding-y:              $input-btn-padding-y !default;
$pagination-padding-x:              $input-btn-padding-x !default;

$pagination-color:                  $white !default;
$pagination-bg:                     transparent !default;
$pagination-border-color:           rgba($black, .6) !default;
$pagination-hover-color:            $white !default;
$pagination-hover-bg:               transparent !default;
$pagination-hover-border-color:     rgba($black, .6) !default;
$pagination-active-bg:              transparent !default;
$pagination-active-border-color:    rgba($black, .6) !default;
$pagination-disabled-bg:            transparent !default;
$pagination-disabled-border-color:  rgba($black, .6) !default;

// Cards

$card-border-color:                 rgba($black, .6) !default;
$card-cap-bg:                       lighten($gray-800, 10%) !default;
$card-bg:                           lighten($body-bg, 5%) !default;

// Accordions

$accordion-bg:                      lighten($body-bg, 5%) !default;
$accordion-border-color:            $table-border-color !default;
$accordion-button-active-color:     $white !default;

// Popovers

$popover-bg:                        lighten($body-bg, 5%) !default;

// Toasts

$toast-background-color:            lighten($body-bg, 5%) !default;
$toast-border-color:                rgba(0, 0, 0, .2) !default;
$toast-header-color:                $body-color !default;
$toast-header-background-color:     $toast-background-color !default;
$toast-header-border-color:         $toast-border-color !default;

// Modals

$modal-content-bg:                  lighten($body-bg, 5%) !default;
$modal-header-border-color:         rgba(0, 0, 0, .2) !default;

// Progress bars

$progress-bg:                       darken($gray-900, 5%) !default;
$progress-bar-color:                $gray-600 !default;

// List group

$list-group-color:                  $white !default;
$list-group-bg:                     lighten($body-bg, 5%) !default;
$list-group-border-color:           rgba($black, .6) !default;
$list-group-item-bg-scale:          0% !default;
$list-group-hover-bg:               lighten($body-bg, 10%) !default;
$list-group-active-color:           $white !default;
$list-group-active-bg:              $list-group-hover-bg !default;
$list-group-active-border-color:    $list-group-border-color !default;
$list-group-disabled-color:         $gray-700 !default;
$list-group-action-color:           $white !default;

// Breadcrumbs

$breadcrumb-padding-y:              .375rem !default;
$breadcrumb-padding-x:              .75rem !default;
$breadcrumb-active-color:           $gray-500 !default;
$breadcrumb-border-radius:          .25rem !default;

// Code

$pre-color:                         inherit !default;
