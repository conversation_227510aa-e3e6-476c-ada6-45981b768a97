// Brite 5.3.5
// Bootswatch

$theme: "brite" !default;

//
// Color system
//

$white:    #fff !default;
$gray-100: #f8f9fa !default;
$gray-200: #e9ecef !default;
$gray-300: #dee2e6 !default;
$gray-400: #ced4da !default;
$gray-500: #adb5bd !default;
$gray-600: #868e96 !default;
$gray-700: #495057 !default;
$gray-800: #343a40 !default;
$gray-900: #212529 !default;
$black:    #000 !default;

$blue:    #61bcff !default;
$indigo:  #828df9 !default;
$purple:  #be82fa !default;
$pink:    #ea4998 !default;
$red:     #f56565 !default;
$orange:  #fa984a !default;
$yellow:  #ffc700 !default;
$green:   #68d391 !default;
$teal:    #2ed3be !default;
$cyan:    #22d2ed !default;
$lime:    #a2e436 !default;

$primary:       $lime !default;
$secondary:     $white !default;
$success:       $green !default;
$info:          $cyan !default;
$warning:       $yellow !default;
$danger:        $red !default;
$light:         $gray-200 !default;
$dark:          $black !default;

// $min-contrast-ratio:   1.75 !default;

$spacer: 1rem !default;

// Body

// Links

$link-color:                              $black !default;

// Components

$border-radius:               .375rem !default;
$border-radius-sm:            .25rem !default;
$border-radius-lg:            .5rem !default;

$component-active-color:      $black !default;
// $component-active-bg:         $black !default;

$focus-ring-width:      1px !default;
$focus-ring-opacity:    1 !default;
$focus-ring-color:      $black !default;

// Fonts

$font-size-base:              .875rem !default;
$font-size-sm:                $font-size-base * .875 !default;

$lead-font-weight:            400 !default;

$headings-font-weight:        500 !default;

$hr-border-width:             2px !default;
$hr-opacity:                  1 !default;

// Tables

$table-cell-padding-y:        .75rem !default;
$table-cell-padding-x:        .75rem !default;

$table-border-width:          1px !default;
$table-border-color:          $black !default;

$table-hover-bg-factor:       0 !default;

$table-border-factor:         1 !default;

$table-bg-scale:              0% !default;

$table-variants: (
  "primary":    $primary,
  "secondary":  $secondary,
  "success":    $success,
  "info":       $info,
  "warning":    $warning,
  "danger":     $danger,
  "light":      $light,
  "dark":       $dark,
) !default;

// Buttons + Forms

$input-btn-padding-y:         .5rem !default;
$input-btn-padding-x:         1rem !default;

$input-btn-padding-y-sm:      .25rem !default;
$input-btn-padding-x-sm:      .75rem !default;
$input-btn-font-size-sm:      $font-size-sm !default;

$input-btn-padding-y-lg:      .75rem !default;
$input-btn-padding-x-lg:      1.25rem !default;
$input-btn-font-size-lg:      $font-size-base !default;

$input-btn-border-width:      2px !default;

// Buttons

$btn-border-width:            2px !default;

// Forms

$form-label-font-weight:                $headings-font-weight !default;

$input-focus-border-color:              $black !default;

$form-check-input-checked-border-color:   $black !default;

$form-range-track-height:         .6rem !default;

$form-range-thumb-width:                   1.2rem !default;
$form-range-thumb-height:                  $form-range-thumb-width !default;
$form-range-thumb-border:                  2px solid $black !default;
$form-range-thumb-box-shadow:              none !default;
$form-range-thumb-disabled-bg:             $light !default;

// Dropdowns

// $dropdown-link-hover-color:     $white !default;
// $dropdown-link-hover-bg:        $primary !default;

// Navs

$nav-link-color:                    $black !default;
$nav-link-hover-color:              $black !default;

$nav-tabs-link-hover-border-color:  transparent !default;

// Navbar

$navbar-padding-y:                  $spacer * .75 !default;
$navbar-padding-x:                  null !default;

$navbar-nav-link-padding-x:         1rem !default;

$navbar-light-color:                $black !default;
$navbar-light-hover-color:          $black !default;
$navbar-light-active-color:         $black !default;
$navbar-light-disabled-color:       rgba($black, .3) !default;
$navbar-light-icon-color:           $black !default;
$navbar-light-toggler-border-color: $black !default;

$navbar-dark-color:                $white !default;
$navbar-dark-hover-color:          $white !default;
$navbar-dark-active-color:         $white !default;
$navbar-dark-disabled-color:       rgba($white, .3) !default;
$navbar-dark-icon-color:           $white !default;
$navbar-dark-toggler-border-color: $white !default;

// Dropdowns

$dropdown-border-color:             $black !default;
$dropdown-border-width:             2px !default;

$dropdown-link-hover-bg:            $primary !default;

$dropdown-header-color:             $black !default;

// Pagination

$pagination-color:                  $black !default;
$pagination-hover-color:            $black !default;
$pagination-hover-bg:               transparent !default;
$pagination-active-border-color:    $black !default;

// Cards

$card-border-color:                 $black !default;

// Accordion

$accordion-button-active-bg:              $primary !default;
$accordion-button-active-color:           $black !default;

// Tooltips

$tooltip-opacity:                   1 !default;

// Popovers

$popover-border-color:              $black !default;

// Toasts

$toast-border-color:                $black !default;
$toast-box-shadow:                  3px 3px 0 0 $black !default;

$toast-header-color:                $black !default;

// Badges

$badge-color:                       $black !default;

// Modals

$modal-content-border-color:        $black !default;

// Alerts

// List group

$list-group-active-border-color:    $black !default;

// Breadcrumbs

$breadcrumb-padding-y:              .5rem !default;
$breadcrumb-padding-x:              1rem !default;
$breadcrumb-divider-color:          $black !default;
$breadcrumb-active-color:           $black !default;
$breadcrumb-border-radius:          $border-radius-lg !default;

// Close

$btn-close-opacity:          1 !default;
$btn-close-hover-opacity:    1 !default;
