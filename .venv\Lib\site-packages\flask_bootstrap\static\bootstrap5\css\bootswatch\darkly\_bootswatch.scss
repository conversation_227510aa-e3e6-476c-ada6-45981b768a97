// Darkly 5.3.5
// Bootswatch


// Variables

$web-font-path: "https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,400;0,700;1,400&display=swap" !default;
@if $web-font-path {
  @import url("#{$web-font-path}");
}

// Typography

.blockquote {
  &-footer {
    color: $gray-600;
  }
}

.text-secondary {
  color: var(--bs-secondary-color) !important;
}

// Forms

@include color-mode(dark) {
  .input-group-text {
    // color: $white;
  }
}

.form-floating {
  > label,
  > .form-control:focus ~ label,
  > .form-control:not(:placeholder-shown) ~ label {
    color: $input-placeholder-color;
  }
}

// Navs

.breadcrumb a {
  color: $white;
}

.pagination {
  a:hover {
    text-decoration: none;
  }
}

// Indicators

.alert {
  color: $white;
  border: none;

  a,
  .alert-link {
    color: $white;
    text-decoration: underline;
  }

  @each $color, $value in $theme-colors {
    &-#{$color} {
      @if $enable-gradients {
        background: $value linear-gradient(180deg, mix($white, $value, 15%), $value) repeat-x;
      } @else {
        background-color: $value;
      }
    }
  }
}

.tooltip {
  --bs-tooltip-bg: var(--bs-tertiary-bg);
  --bs-tooltip-color: var(--bs-emphasis-color);
}
