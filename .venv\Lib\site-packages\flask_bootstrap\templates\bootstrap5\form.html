{#  This file was part of Flask-Bootstrap and was modified under the terms of
 its BSD License. Copyright (c) 2013, <PERSON>. All rights reserved. #}

{% macro render_hidden_errors(form) %}
    {%- if form.errors %}
        {%- for fieldname, errors in form.errors.items() %}
            {%- if bootstrap_is_hidden_field(form[fieldname]) %}
                {%- for error in errors %}
                    <div class="invalid-feedback d-block">{{ error }}</div>
                {%- endfor %}
            {%- endif %}
        {%- endfor %}
    {%- endif %}
{%- endmacro %}

{% macro _hz_form_wrap(horizontal_columns, form_type, add_group=False, required=False) %}
    {% if form_type == "horizontal" %}
        {% if add_group %}
            <div class="{{ form_group_classes }} row{% if required %} required{% endif %}">{% endif %}
        <div class="offset-{{ horizontal_columns[0] }}-{{ horizontal_columns[1] }}
              col-{{ horizontal_columns[0] }}-{{ horizontal_columns[2] }}
             ">
    {% endif %}
{{ caller() }}

{% if form_type == "horizontal" %}
    {% if add_group %}</div>{% endif %}
    </div>
{% endif %}
{% endmacro %}

{% macro render_field(field,
                    form_type="basic",
                    horizontal_columns=('lg', 2, 10),
                    button_map={},
                    button_style='',
                    button_size='',
                    form_group_classes='') %}

    {# this is a workaround hack for the more straightforward-code of just passing required=required parameter. older versions of wtforms do not have
the necessary fix for required=False attributes, but will also not set the required flag in the first place. we skirt the issue using the code below #}
    {% if field.flags.required and not required in kwargs %}
        {% set kwargs = dict(required=True, **kwargs) %}
    {% endif %}

    {% set form_group_classes = form_group_classes or config.BOOTSTRAP_FORM_GROUP_CLASSES %}

    {# support for label_class, radio_class and description_class which are popped, to prevent they are added to input, but restored at the end of this macro for the next rendering #}
    {%- set label_class = '' -%}
    {%- if field.render_kw.label_class -%}
        {% set label_class = field.render_kw.pop('label_class', '') -%}
        {% set label_classes = ' ' + label_class -%}
    {%- endif -%}
    {%- set radio_class = '' -%}
    {%- if field.render_kw.radio_class -%}
        {% set radio_class = field.render_kw.pop('radio_class', '') -%}
        {% set radio_classes = ' ' + radio_class -%}
    {%- endif -%}
    {%- set description_class = '' -%}
    {%- if field.render_kw.description_class -%}
        {% set description_class = field.render_kw.pop('description_class', '') -%}
        {% set description_classes = ' ' + description_class -%}
    {%- endif -%}

    {# combine render_kw class or class/class_ argument with Bootstrap classes #}
    {% set render_kw_class = ' ' + field.render_kw.class if field.render_kw.class else '' %}
    {% set class = kwargs.pop('class', '') or kwargs.pop('class_', '') %}
    {% if class %}
        {# override render_kw class when class/class_ presents as keyword argument #}
        {% set render_kw_class = '' %}
        {% set render_kw_class_ = '' %}
        {% set class = ' ' + class %}
    {% endif %}
    {% set extra_classes = render_kw_class + class %}

    {% if field.widget.input_type == 'checkbox' %}
        {% set field_kwargs = kwargs %}
        {% call _hz_form_wrap(horizontal_columns, form_type, True, required=required) %}
        {% if field.type == 'SwitchField' %}
            {% do field_kwargs.update({'role': 'switch'}) %}
        {% endif %}
            <div class="{{ form_group_classes }} form-check{% if field.type == 'SwitchField' %} form-switch{% endif %}{% if form_type == "inline" %} form-check-inline{% endif %}">
                {%- if field.errors %}
                    {{ field(class="form-check-input is-invalid%s" % extra_classes, **field_kwargs)|safe }}
                {%- else -%}
                    {{ field(class="form-check-input%s" % extra_classes, **field_kwargs)|safe }}
                {%- endif %}
                {{ field.label(class="form-check-label%s" % label_classes, for=field.id)|safe }}
                {%- if field.errors %}
                    {%- for error in field.errors %}
                        <div class="invalid-feedback d-block">{{ error }}</div>
                    {%- endfor %}
                {%- elif field.description -%}
                    {% call _hz_form_wrap(horizontal_columns, form_type, required=required) %}
                        <small class="form-text text-body-secondary{{ description_classes|safe }}">{{ field.description|safe }}</small>
                    {% endcall %}
                {%- endif %}
            </div>
        {% endcall %}
    {%- elif field.type == 'RadioField' -%}
        {# note: A cleaner solution would be rendering depending on the widget,
     this is just a hack for now, until I can think of something better #}
        <div class="{{ form_group_classes }} {% if form_type == 'horizontal' %}row{% endif %}{% if required %} required{% endif %}">
            {%- if form_type == "inline" %}
                {{ field.label(class="visually-hidden%s" % label_classes)|safe }}
            {% elif form_type == "horizontal" %}
                {{ field.label(class="col-form-label" + (
                " col-%s-%s" % horizontal_columns[0:2]) + (
                "%s" % label_classes))|safe }}
            {%- else -%}
                {{ field.label(class="form-label%s" % label_classes)|safe }}
            {% endif %}
            {% if form_type == 'horizontal' %}
              <div class="col-{{ horizontal_columns[0] }}-{{ horizontal_columns[2] }}">
              {% endif %}
            {#% call _hz_form_wrap(horizontal_columns, form_type, True, required=required) %#}
            {% for item in field -%}
                <div class="form-check{% if form_type == "inline" %} form-check-inline{% endif %}">
                    {{ item(class="form-check-input")|safe }}
                    {{ item.label(class="form-check-label%s" % radio_classes, for=item.id)|safe }}
                </div>
            {% endfor %}
        {#% endcall %#}
        {% if form_type == 'horizontal' %}
          </div>
        {% endif %}
        {%- if field.errors %}
            {%- for error in field.errors %}
                <div class="invalid-feedback d-block">{{ error }}</div>
            {%- endfor %}
        {%- elif field.description -%}
            <small class="form-text text-body-secondary{{ description_classes|safe }}">{{ field.description|safe }}</small>
        {%- endif %}
        </div>
    {%- elif field.type == 'SubmitField' -%}
        {# deal with jinja scoping issues? #}
        {% set field_kwargs = kwargs %}
        {# note: same issue as above - should check widget, not field type #}
        {% call _hz_form_wrap(horizontal_columns, form_type, True, required=required) %}
            {% set default_button_style = button_style or config.BOOTSTRAP_BTN_STYLE %}
            {% set default_button_size = button_size or config.BOOTSTRAP_BTN_SIZE %}
            {{ field(class='btn btn-%s btn-%s%s' % (button_map.get(field.name, default_button_style), default_button_size, extra_classes), **field_kwargs) }}
        {% endcall %}
    {%- elif field.type in ['CSRFTokenField', 'HiddenField'] -%}
        {{ field()|safe }}
    {%- elif field.type in ['FormField', 'FieldList'] -%}
        {# note: FormFields are tricky to get right and complex setups requiring
   these are probably beyond the scope of what this macro tries to do.
   the code below ensures that things don't break horribly if we run into
   one, but does not try too hard to get things pretty. #}
        <fieldset>
            <legend>{{ field.label }}</legend>
            {%- for subfield in field %}
                {% if not bootstrap_is_hidden_field(subfield) -%}
                    {{ render_field(subfield,
                      form_type=form_type,
                      horizontal_columns=horizontal_columns,
                      button_map=button_map) }}
                {%- endif %}
            {%- endfor %}
        </fieldset>
    {% else -%}
        <div class="{{ form_group_classes }}{%- if form_type == "horizontal" %} row{% endif -%}
                         {%- if field.flags.required %} required{% endif -%}">
            {%- if form_type == "inline" %}
                {{ field.label(class="visually-hidden%s" % label_classes)|safe }}
                {%- if field.type in ['DecimalRangeField', 'IntegerRangeField'] %}
                    {% if field.errors %}
                        {{ field(class="form-range is-invalid%s" % extra_classes, **kwargs)|safe }}
                    {% else %}
                        {{ field(class="form-range%s" % extra_classes, **kwargs)|safe }}
                    {% endif %}
                {%- elif field.type == 'SelectField' -%}
                    {% if field.errors %}
                        {{ field(class="form-select mb-2 mr-sm-2 mb-sm-0 is-invalid%s" % extra_classes, **kwargs)|safe }}
                    {% else %}
                        {{ field(class="form-select mb-2 mr-sm-2 mb-sm-0%s" % extra_classes, **kwargs)|safe }}
                    {% endif %}
                {%- else -%}
                    {% if field.errors %}
                        {{ field(class="form-control mb-2 mr-sm-2 mb-sm-0 is-invalid%s" % extra_classes, **kwargs)|safe }}
                    {% else %}
                        {{ field(class="form-control mb-2 mr-sm-2 mb-sm-0%s" % extra_classes, **kwargs)|safe }}
                    {% endif %}
                {%- endif %}
            {% elif form_type == "horizontal" %}
                {{ field.label(class="col-form-label" + (
                " col-%s-%s" % horizontal_columns[0:2]) + (
                "%s" % label_classes))|safe }}
                <div class="col-{{ horizontal_columns[0] }}-{{ horizontal_columns[2] }}">
                {%- if field.type in ['DecimalRangeField', 'IntegerRangeField'] %}
                    {% if field.errors %}
                        {{ field(class="form-range is-invalid%s" % extra_classes, **kwargs)|safe }}
                    {% else %}
                        {{ field(class="form-range%s" % extra_classes, **kwargs)|safe }}
                    {% endif %}
                {%- elif field.type == 'SelectField' -%}
                    {% if field.errors %}
                        {{ field(class="form-select is-invalid%s" % extra_classes, **kwargs)|safe }}
                    {% else %}
                        {{ field(class="form-select%s" % extra_classes, **kwargs)|safe }}
                    {% endif %}
                {%- else -%}
                    {% if field.errors %}
                        {{ field(class="form-control is-invalid%s" % extra_classes, **kwargs)|safe }}
                    {% else %}
                        {{ field(class="form-control%s" % extra_classes, **kwargs)|safe }}
                    {% endif %}
                {%- endif %}
                </div>
                {%- if field.errors %}
                    {%- for error in field.errors %}
                        {% call _hz_form_wrap(horizontal_columns, form_type, required=required) %}
                            <div class="invalid-feedback d-block">{{ error }}</div>
                        {% endcall %}
                    {%- endfor %}
                {%- elif field.description -%}
                    {% call _hz_form_wrap(horizontal_columns, form_type, required=required) %}
                        <small class="form-text text-body-secondary{{ description_classes|safe }}">{{ field.description|safe }}</small>
                    {% endcall %}
                {%- endif %}
            {%- else -%}
                {{ field.label(class="form-label%s" % label_classes)|safe }}
                {%- if field.type in ['DecimalRangeField', 'IntegerRangeField'] %}
                    {% if field.errors %}
                        {{ field(class="form-range is-invalid%s" % extra_classes, **kwargs)|safe }}
                    {% else %}
                        {{ field(class="form-range%s" % extra_classes, **kwargs)|safe }}
                    {% endif %}
                {%- elif field.type == 'SelectField' -%}
                    {% if field.errors %}
                        {{ field(class="form-select is-invalid%s" % extra_classes, **kwargs)|safe }}
                    {% else %}
                        {{ field(class="form-select%s" % extra_classes, **kwargs)|safe }}
                    {% endif %}
                {%- else -%}
                    {% if field.errors %}
                        {{ field(class="form-control is-invalid%s" % extra_classes, **kwargs)|safe }}
                    {% else %}
                        {{ field(class="form-control%s" % extra_classes, **kwargs)|safe }}
                    {% endif %}
                {%- endif %}
                {%- if field.errors %}
                    {%- for error in field.errors %}
                        <div class="invalid-feedback d-block">{{ error }}</div>
                    {%- endfor %}
                {%- elif field.description -%}
                    <small class="form-text text-body-secondary{{ description_classes|safe }}">{{ field.description|safe }}</small>
                {%- endif %}
            {%- endif %}
        </div>
    {% endif %}

    {%- if label_class -%}
        {%- set _ = field.render_kw.update({'label_class': label_class}) -%}
    {%- endif -%}
    {%- if radio_class -%}
        {%- set _ = field.render_kw.update({'radio_class': radio_class}) -%}
    {%- endif -%}
    {%- if description_class -%}
        {%- set _ = field.render_kw.update({'description_class': description_class}) -%}
    {%- endif -%}
{% endmacro %}

{# valid form types are "basic", "inline" and "horizontal" #}
{% macro render_form(form,
                    action="",
                    method="post",
                    extra_classes=None,
                    role="form",
                    form_type="basic",
                    horizontal_columns=('lg', 2, 10),
                    enctype=None,
                    button_map={},
                    button_style="",
                    button_size="",
                    id="",
                    novalidate=False,
                    render_kw={},
                    form_group_classes='',
                    form_inline_classes='') %}
    {#-
action="" is what we want, from http://www.ietf.org/rfc/rfc2396.txt:

4.2. Same-document References

   A URI reference that does not contain a URI is a reference to the
   current document.  In other words, an empty URI reference within a
   document is interpreted as a reference to the start of that document,
   and a reference containing only a fragment identifier is a reference
   to the identified fragment of that document.  Traversal of such a
   reference should not result in an additional retrieval action.
   However, if the URI reference occurs in a context that is always
   intended to result in a new request, as in the case of HTML's FORM
   element, then an empty URI reference represents the base URI of the
   current document and should be replaced by that URI when transformed
   into a request.

 -#}
    {#- if any file fields are inside the form and enctype is automatic, adjust
    if file fields are found. could really use the equalto test of jinja2
    here, but latter is not available until 2.8

    warning: the code below is guaranteed to make you cry =(
#}
    {%- set form_inline_classes = form_inline_classes or config.BOOTSTRAP_FORM_INLINE_CLASSES %}
    {%- set _enctype = [] %}
    {%- if enctype is none -%}
        {%- for field in form %}
            {%- if field.type in ['FileField', 'MultipleFileField'] %}
                {#- for loops come with a fairly watertight scope, so this list-hack is
          used to be able to set values outside of it #}
                {%- set _ = _enctype.append('multipart/form-data') -%}
            {%- endif %}
        {%- endfor %}
    {%- else %}
        {% set _ = _enctype.append(enctype) %}
    {%- endif %}
    {%- if form_type == "inline" %}{% set form_group_classes = 'col-12' %}{%- endif %}
    <form{%- if action != None %} action="{{ action }}"{% endif -%}
            {%- if id %} id="{{ id }}"{% endif -%}
            {%- if method %} method="{{ method }}"{% endif %}
            class="form
{%- if extra_classes %} {{ extra_classes }}{% endif -%}{%- if form_type == "inline" %} {{ form_inline_classes }}{% endif -%}"
            {%- if _enctype[0] %} enctype="{{ _enctype[0] }}"{% endif -%}
            {%- if role %} role="{{ role }}"{% endif -%}
            {%- if novalidate %} novalidate{% endif -%}
            {%- if render_kw %} {{ render_kw|xmlattr }}{% endif -%}>
        {{ form.hidden_tag() }}
        {{ render_hidden_errors(form) }}
        {%- for field in form %}
            {% if not bootstrap_is_hidden_field(field) -%}
                {{ render_field(field,
                    form_type=form_type,
                    horizontal_columns=horizontal_columns,
                    button_map=button_map,
                    button_style=button_style,
                    button_size=button_size,
                    form_group_classes=form_group_classes) }}
            {%- endif %}
        {%- endfor %}
    </form>
{%- endmacro %}

{% macro render_form_row(fields,
    row_class='row',
    col_class_default='col',
    col_map={},
    button_map={},
    button_style='',
    button_size='',
    form_group_classes='',
    form_type='basic',
    horizontal_columns=('lg', 2, 10)) %}
<div class="{{ row_class }}">
  {% for field in fields %}
    {% if field.name in col_map %}
      {% set col_class = col_map[field.name] %}
    {% else %}
      {% set col_class = col_class_default %}
    {% endif %}
    <div class="{{ col_class }}">
      {{ render_field(field,
          button_map=button_map,
          button_style=button_style,
          button_size=button_size,
          form_group_classes=form_group_classes,
          form_type=form_type,
          horizontal_columns=horizontal_columns) }}
    </div>
  {% endfor %}
</div>
{% endmacro %}
