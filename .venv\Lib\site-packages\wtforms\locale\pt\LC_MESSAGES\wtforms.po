# Portuguese translations for WTForms.
# Copyright (C) 2020 WTForms Team
# This file is distributed under the same license as the WTForms project.
# <AUTHOR> <EMAIL>, 2020.
#
msgid ""
msgstr ""
"Project-Id-Version: WTForms 2.0dev\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2024-01-11 08:20+0100\n"
"PO-Revision-Date: 2024-06-17 21:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Portuguese <https://hosted.weblate.org/projects/wtforms/"
"wtforms/pt/>\n"
"Language: pt\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.6-dev\n"
"Generated-By: Babel 2.8.0\n"

#: src/wtforms/validators.py:86
#, python-format
msgid "Invalid field name '%s'."
msgstr "Nome do campo inválido '%s'."

#: src/wtforms/validators.py:99
#, python-format
msgid "Field must be equal to %(other_name)s."
msgstr "O campo deve ser igual a %(other_name)s."

#: src/wtforms/validators.py:145
#, python-format
msgid "Field must be at least %(min)d character long."
msgid_plural "Field must be at least %(min)d characters long."
msgstr[0] "O campo deve ter pelo menos %(min)d caracteres."
msgstr[1] "Os campos devem ter pelo menos %(min)d caracteres."

#: src/wtforms/validators.py:151
#, python-format
msgid "Field cannot be longer than %(max)d character."
msgid_plural "Field cannot be longer than %(max)d characters."
msgstr[0] "O campo não pode ter mais do que %(max)d caracteres."
msgstr[1] "Os campos não podem ter mais do que %(max)d caracteres."

#: src/wtforms/validators.py:157
#, python-format
msgid "Field must be exactly %(max)d character long."
msgid_plural "Field must be exactly %(max)d characters long."
msgstr[0] "O campo deve ter exatamente %(max)d caracteres."
msgstr[1] "Os campos devem ter exatamente %(max)d caracteres."

#: src/wtforms/validators.py:163
#, python-format
msgid "Field must be between %(min)d and %(max)d characters long."
msgstr "O campo deve ter entre %(min)d e %(max)d caracteres."

#: src/wtforms/validators.py:216
#, python-format
msgid "Number must be at least %(min)s."
msgstr "O valor não pode ser menos do que %(min)s."

#: src/wtforms/validators.py:219
#, python-format
msgid "Number must be at most %(max)s."
msgstr "O valor não pode ser mais do que %(max)s."

#: src/wtforms/validators.py:222
#, python-format
msgid "Number must be between %(min)s and %(max)s."
msgstr "O valor tem que ser entre %(min)s e %(max)s."

#: src/wtforms/validators.py:293 src/wtforms/validators.py:323
msgid "This field is required."
msgstr "Campo obrigatório."

#: src/wtforms/validators.py:358
msgid "Invalid input."
msgstr "Entrada inválida."

#: src/wtforms/validators.py:422
msgid "Invalid email address."
msgstr "Email inválido."

#: src/wtforms/validators.py:460
msgid "Invalid IP address."
msgstr "IP inválido."

#: src/wtforms/validators.py:503
msgid "Invalid Mac address."
msgstr "Mac address inválido."

#: src/wtforms/validators.py:540
msgid "Invalid URL."
msgstr "URL inválido."

#: src/wtforms/validators.py:561
msgid "Invalid UUID."
msgstr "UUID inválido."

#: src/wtforms/validators.py:594
#, python-format
msgid "Invalid value, must be one of: %(values)s."
msgstr "Valor inválido, deve ser um dos seguintes: %(values)s."

#: src/wtforms/validators.py:629
#, python-format
msgid "Invalid value, can't be any of: %(values)s."
msgstr "Valor inválido, não deve ser um dos seguintes: %(values)s."

#: src/wtforms/validators.py:698
msgid "This field cannot be edited."
msgstr "Este campo não pode ser editado."

#: src/wtforms/validators.py:714
msgid "This field is disabled and cannot have a value."
msgstr "Campo desativado: não pode ter um valor."

#: src/wtforms/csrf/core.py:96
msgid "Invalid CSRF Token."
msgstr "Token CSRF inválido."

#: src/wtforms/csrf/session.py:63
msgid "CSRF token missing."
msgstr "Falta o token CSRF."

#: src/wtforms/csrf/session.py:71
msgid "CSRF failed."
msgstr "CSRF falhou."

#: src/wtforms/csrf/session.py:76
msgid "CSRF token expired."
msgstr "Token CSRF expirado."

#: src/wtforms/fields/choices.py:142
msgid "Invalid Choice: could not coerce."
msgstr "Escolha inválida: não é possível calcular."

#: src/wtforms/fields/choices.py:149 src/wtforms/fields/choices.py:203
msgid "Choices cannot be None."
msgstr "A escolha não pode ser None."

#: src/wtforms/fields/choices.py:155
msgid "Not a valid choice."
msgstr "Escolha inválida."

#: src/wtforms/fields/choices.py:193
msgid "Invalid choice(s): one or more data inputs could not be coerced."
msgstr "Escolha(s) inválida(s): não é possível calcular alguns dos valores."

#: src/wtforms/fields/choices.py:214
#, python-format
msgid "'%(value)s' is not a valid choice for this field."
msgid_plural "'%(value)s' are not valid choices for this field."
msgstr[0] "‘%(value)s’ não é uma escolha válida para este campo."
msgstr[1] "‘%(value)s’ não são escolhas válidas para este campo."

#: src/wtforms/fields/datetime.py:51
msgid "Not a valid datetime value."
msgstr "O valor temporal não é válido."

#: src/wtforms/fields/datetime.py:77
msgid "Not a valid date value."
msgstr "A data não é válida."

#: src/wtforms/fields/datetime.py:103
msgid "Not a valid time value."
msgstr "Não é um valor de tempo válido."

#: src/wtforms/fields/datetime.py:148
msgid "Not a valid week value."
msgstr "Não é um valor de semana válido."

#: src/wtforms/fields/numeric.py:82 src/wtforms/fields/numeric.py:92
msgid "Not a valid integer value."
msgstr "O valor inteiro não é válido."

#: src/wtforms/fields/numeric.py:168
msgid "Not a valid decimal value."
msgstr "O valor decimal não é válido."

#: src/wtforms/fields/numeric.py:197
msgid "Not a valid float value."
msgstr "O valor com vírgula flutuante não é válido."
